<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.editprofile')) }}</h3>
        </template>
        <div class="row">
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <profile-entry />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <change-password />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <delete-student />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useLang from "../../composables/useLang.js";
import useProfile from "../../composables/useProfile.js";
import useToast from "../../composables/useToast.js";
import Panel from "../Layout/bs5/Panel4.vue";
import ProfileEntry from "./ProfileEntry.vue";
import ChangePassword from "./ChangePassword.vue";
import DeleteStudent from "./DeleteStudent.vue";

const { ucFirst, translate } = useLang();
const { busy, getProfile } = useProfile();
const { failToast } = useToast();

onMounted(async () => {
    try {
        await getProfile();
    } catch (error) {
        failToast(error.message);
    }
});
</script>

<style scoped>

</style>
