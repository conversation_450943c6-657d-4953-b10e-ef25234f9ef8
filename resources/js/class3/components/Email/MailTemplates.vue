<template>
    <panel :busy="busy">
        <template v-slot:title>
            {{ ucFirst(translate('generic.mailtemplates')) }}
        </template>
        <template v-slot:subtitle>
            <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#newTemplatePopup">
                <i class="fa fa-plus"></i>
                {{ ucFirst(translate('generic.new')) }}
            </button>
        </template>
        <div class="d-flex flex-wrap align-items-center">
            <div class="me-2 mb-2">
                <select v-model="selectedTemplateId" class="form-select">
                    <option value="0">{{ ucFirst(translate('generic.pleasechooseanoption')) }}</option>
                    <option v-for="mtemplate in mailTemplates" :key="mtemplate.id" :value="mtemplate.id">
                        {{ mtemplate.created_at.substr(0, 10) }} - {{ mtemplate.label }}
                    </option>
                </select>
            </div>

            <div class="mb-2" v-if="selectedTemplateId > 0">
                <span
                    v-tooltip="ucFirst(translate('generic.deletetemplate')) + '?'"
                >
                    <button
                        type="button"
                        data-bs-toggle="modal"
                        data-bs-target="#delTemplate"
                        class="btn btn-sm btn-danger"
                    >
                        <i class="fas fa-trash"></i>
                    </button>
                </span>
            </div>

            <!-- Dropdown target  -->
            <div
                class="d-flex flex-wrap align-items-center ms-2 mb-2"
                v-if="selectedTemplateId > 0"
            >
                <strong class="me-2">{{ ucFirst(translate('generic.templatetarget')) }}:</strong>

                <div class="btn-group" role="group" aria-label="Template targets">
                    <label for="edit_templatetargeta"
                           class="btn btn-outline-secondary d-flex align-items-center"
                           :class="{'active': selectedMailTemplate.targets === 'a'}"
                    >
                        {{ ucFirst(translate('mailtargets.a')) }}
                        <input id="edit_templatetargeta" type="radio" class="d-none" value="a"
                               v-model="selectedMailTemplate.targets">
                    </label>
                    <label for="edit_templatetargetb"
                           class="btn btn-outline-secondary d-flex align-items-center"
                           :class="{'active': selectedMailTemplate.targets === 'b'}"
                    >
                        {{ ucFirst(translate('mailtargets.b')) }}
                        <input id="edit_templatetargetb" type="radio" class="d-none" value="b"
                               v-model="selectedMailTemplate.targets">
                    </label>
                    <label for="edit_templatetargetc"
                           class="btn btn-outline-secondary d-flex align-items-center"
                           :class="{'active': selectedMailTemplate.targets === 'c'}"
                    >
                        {{ ucFirst(translate('mailtargets.c')) }}
                        <input id="edit_templatetargetc" type="radio" class="d-none" value="c"
                               v-model="selectedMailTemplate.targets">
                    </label>
                </div>
            </div>
        </div>
        <template v-if="selectedTemplateId > 0">
            <div class="row">
                <div class="col-lg-9    ">
                    <strong>
                        {{ ucFirst(translate('generic.templatetext')) }}
                    </strong>

                    <ckeditor
                        v-if="ClassicEditor && configMailTemplate"
                        v-model="selectedMailTemplate.content"
                        :editor="ClassicEditor"
                        :config="configMailTemplate"
                    />
                </div>

                <div
                    class="col-lg-3"
                >
                    <strong>
                        {{ ucFirst(translateChoice('generic.variables', 2)) }}
                    </strong>
                    <span
                        v-tooltip="ucFirst(translate('generic.explainvariables'))"
                    >
                        <i
                            class="fa fa-question-circle"
                        ></i>
                    </span>
                    <br>
                    <list-template-variables></list-template-variables>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <!--extra space - fixme-->
                </div>
            </div>
        </template>
        <floating-bar :add-home-button="true">
            <button class="btn btn-primary" @click="saveTextEntry" :disabled="!dirty">
                <font-awesome-icon icon="fa-solid fa-save" />
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </floating-bar>

        <modal :popup-title="ucFirst(translate('generic.newtemplate'))"
               :closetext="ucFirst(translate('generic.close'))"
               modal-id="newTemplatePopup"
        >
            <div class="mb-3">
                <label for="newlabel" class="form-label">
                    <strong>{{ ucFirst(translate('generic.newtemplatelabel')) }}</strong>
                </label>
                <input id="newlabel" type="text" class="form-control" v-model="newLabel">
            </div>

            <label class="form-label">
                <strong>{{ ucFirst(translate('generic.templatetarget')) }}</strong>
            </label>

            <div class="btn-group mb-3" role="group" aria-label="New template targets">
                <label for="new_templatetargeta"
                       class="btn btn-outline-secondary d-flex align-items-center"
                       :class="{'active': newTemplateTarget === 'a'}"
                >
                    {{ ucFirst(translate('mailtargets.a')) }}
                    <input id="new_templatetargeta" type="radio" class="d-none" value="a" v-model="newTemplateTarget">
                </label>
                <label for="new_templatetargetb"
                       class="btn btn-outline-secondary d-flex align-items-center"
                       :class="{'active': newTemplateTarget === 'b'}"
                >
                    {{ ucFirst(translate('mailtargets.b')) }}
                    <input id="new_templatetargetb" type="radio" class="d-none" value="b" v-model="newTemplateTarget">
                </label>
                <label for="new_templatetargetc"
                       class="btn btn-outline-secondary d-flex align-items-center"
                       :class="{'active': newTemplateTarget === 'c'}"
                >
                    {{ ucFirst(translate('mailtargets.c')) }}
                    <input id="new_templatetargetc" type="radio" class="d-none" value="c" v-model="newTemplateTarget">
                </label>
            </div>

            <template v-slot:okbutton>
                <button type="button"
                        class="btn btn-primary"
                        data-bs-dismiss="modal"
                        :disabled="newLabel === ''"
                        @click.prevent="createNewTemplate()"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </template>
        </modal>
    </panel>
    <!-- Confirm delete template -->
    <are-you-sure
        :button-text="ucFirst(translate('generic.deletetemplate'))"
        @confirmclicked="deleteTemplate"
        modal-id="delTemplate"
    ></are-you-sure>
</template>

<script setup>
import { onMounted, onUpdated, ref, watch } from 'vue';

import { Ckeditor } from '@ckeditor/ckeditor5-vue';

import Panel from '../Layout/bs5/Panel4.vue';
import Modal from '../Layout/bs5/Modal4.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import ListTemplateVariables from './ListTemplateVariables.vue';
import FloatingBar from "../Layout/bs5/FloatingBarBs5.vue";

import useLang from "../../composables/useLang";
import useApi from "../../composables/useApi";
import useToast from "../../composables/useToast";
import useConfigItems from "../../composables/useConfigItems.js";
import useMailTemplates from "../../composables/useMailTemplates";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { translate, translateChoice, ucFirst } = useLang();
const { apiPost, apiPut, apiDel } = useApi();
const { successToast, failToast } = useToast();
const { configMailTemplate, isLayoutReady } = useConfigItems();
const { getTemplates, templates: mailTemplates } = useMailTemplates();

const dirty = ref(false);
const busy = ref(false);
//const mailTemplates = ref(null);
const selectedTemplateId = ref(0);
const selectedMailTemplate = ref(null);
const newLabel = ref('');
const newTemplateTarget = ref('a');

/* ------------------------ */
import {
    ClassicEditor,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

const editor = ClassicEditor;

/* ------------------------ */

const saveTextEntry = async () => {
    busy.value = true;
    const data = {
        id: selectedMailTemplate.value.id,
        mcontent: selectedMailTemplate.value.content,
        mtarget: selectedMailTemplate.value.targets
    };

    try {
        await apiPut('/api/updateMailTemplate', data);
        successToast(
            ucFirst(translate('generic.changessaved'))
        );
        // get all to refresh
        await getTemplates();
        selectedTemplateId.value = selectedMailTemplate.value.id;
        dirty.value = false;
    } catch (err) {
        failToast(
            ucFirst(translate('generic.savingfailed')) + '. ' +
            ucFirst(translateChoice('generic.messages', 1)) + ': ' + err.message,
            ucFirst(translate('generic.error'))
        );
    } finally {
        setTimeout(() => busy.value = false, 500);
    }
};

const createNewTemplate = async () => {
    busy.value = true;
    const data = {
        mlabel: newLabel.value,
        mcontent: ucFirst(translate('generic.pleaseaddcontent')),
        mtarget: newTemplateTarget.value
    };

    try {
        const response = await apiPost('/api/createMailTemplate', data);
        successToast(
            ucFirst(translate('generic.savesuccess'))
        );
        // get all to refresh and activate the newly created template
        await getTemplates();
        selectedTemplateId.value = response.data.newTemplateId;
        dirty.value = false;
    } catch (err) {
        failToast(
            ucFirst(translate('generic.savingfailed')) + '. ' +
            ucFirst(translateChoice('generic.messages', 1)) +
            ': ' + err.message,
            ucFirst(translate('generic.error'))
        );
    } finally {
        setTimeout(() => busy.value = false, 500);
    }
};

const deleteTemplate = async () => {
    busy.value = true;
    try {
        await apiDel(`/api/deleteMailTemplate/${ selectedMailTemplate.value.id }`);
        successToast(
            ucFirst(translate('generic.deletesuccessful'))
        );
        // get all to refresh
        await getTemplates();
    } catch (err) {
        failToast(
            ucFirst(translate('generic.deletefailed')) + '. ' +
            ucFirst(translateChoice('generic.messages', 1)) +
            ': ' + err.message,
            ucFirst(translate('generic.error'))
        );
    } finally {
        setTimeout(() => busy.value = false, 500);
    }
};

// Watchers
watch(selectedTemplateId, () => {
    // should be exactly one
    selectedMailTemplate.value = mailTemplates.value.filter(
        template => selectedTemplateId.value === template.id
    )[0];
});

// quick solution: dirty as soon as a template is chosen
watch(selectedMailTemplate, () => {
    dirty.value = true;
}, { deep: true });

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

onMounted(() => {
    getTemplates();
    isLayoutReady.value = true;
});

onUpdated(() => {
    if (window.Clipboard && typeof window.Clipboard === 'function') {
        // Check if it's a constructor function that needs 'new'
        if (/^class\s/.test(window.Clipboard.toString()) ||
            (window.Clipboard.prototype && window.Clipboard.prototype.constructor === window.Clipboard)) {
            new window.Clipboard('.clipboardTarget');
        }
    }
});
</script>

<style>
</style>
