<template>
    <div>
        <GenericSettings />
        <EmailSettings />
        <floating-bar>
            <button class="btn btn-primary" @click="save">{{ ucFirst(translate('generic.save')) }}</button>
        </floating-bar>
    </div>
</template>

<script setup>
import { onMounted } from "vue";
import GenericSettings from "./GenericSettings.vue";
import EmailSettings from "./EmailSettings.vue";
import FloatingBar from "../Layout/bs5/FloatingBarBs5.vue";
import useDomain from "../../composables/useDomain.js";
import useLang from "../../composables/useLang.js";
import useToast from "../../composables/useToast.js";


const { ucFirst, translate } = useLang();
const { getDomainData, updateDomainData } = useDomain();
const { successToast, failToast } = useToast();

onMounted(async () => {
    await getDomainData();
    addAsteriskOnRequiredFields();
});

const save = async () => {
    try {
        await updateDomainData();
        successToast(translate('generic.saved'), translate('generic.success'), 5000);
        await getDomainData();
        addAsteriskOnRequiredFields();
    } catch (e) {
        failToast(e, translate('generic.error'));
    }
};

const addAsteriskOnRequiredFields = () => {
    const requiredFields = document.querySelectorAll('input[required]');
    requiredFields.forEach((field) => {
        // find the label for the input field
        const label = document.querySelector(`label[for="${field.id}"]`);
        // add an asterisk after the label
        label.insertAdjacentHTML('afterend', '<span class="text-danger ms-1"><strong>*</strong></span>');
    });
};

</script>

<style scoped>

</style>
